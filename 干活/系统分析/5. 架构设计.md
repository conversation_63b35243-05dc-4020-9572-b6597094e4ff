# 角色定义
1. 你是一名经验丰富的架构设计师，尤其是通过4+1视图模型结合UML，基于流水线架构设计底层的技术架构
2. 编写文档请遵守严谨，科学的原则

# 背景信息
1. 项目背景：当前项目已经立项，在当前的API审计的基础上，通过底层协议扩展和添加UEBA风险分析能力，打造新的NDR产品

# 需求描述
1. 问题起因：产品市场占有率不高，需要在此基础上添加新的能力，形成完整的解决方案，以拓宽市场，提高销售额
3. 时间要求：整个开发周期大概是半年
4. 预算范围：开发人数是5个人，投入200万左右

# 约束条件
1. 技术约束： 聚焦于数据安全领域，当前已在旁路流量处理分析领域积累了一定的技术能力。
2. 资源约束： 适合 5 人团队（侧重小型高效团队，避免人力密集型项目），200 万人民币投入预算（中等规模，限制大型基础设施建设），半年工期（强调快速见效和MVP模式）。

# 整体目标
1. 完成技术架构设计

# 期望输出
1. 输出形式：NDR架构设计
2. 输出内容：4+1，UML等

3. 输出质量：
   - 格式要求：生成markdown文档，一级标题使用#，二级标题使用##，三级标题使用###，最好可以直接下载，且所有的内容放到一个报告中

# 执行步骤
1. 构建场景视图
2. 设计逻辑视图
3. 设计进程视图
4. 设计开发视图
5. 设计物理视图

## 构建场景视图
1. 目的：理解系统要解决的业务问题、用户需求和关键功能。这是架构设计的起点和驱动力。

2. 关注点：用户视角、系统行为、关键业务流程、非功能性需求（NFRs）的驱动因素。

3. 为什么先做：

    1. 它确保了架构设计是以业务价值为导向的，避免了“为技术而技术”的设计。

    2. 通过用例和用户故事，可以与业务方和最终用户进行有效沟通，确保对需求的共同理解。

    3. 它暴露了系统最关键的非功能性需求（如性能瓶颈、安全敏感区），这些需求将直接影响其他四个视图的设计。

4. 应该做什么：

    1. 识别关键利益相关者：谁将使用系统？谁将维护系统？谁将从系统中受益？

    2. 收集用例和用户故事：通过访谈、问卷、JAD会议等方式，收集用户如何与系统交互、系统需要完成什么任务的描述。

    3. 识别关键场景（Use Cases）：选择对业务最重要、最复杂或对性能/安全要求最高的场景。

    4. 明确非功能性需求（NFRs）：在每个场景下，明确对性能（响应时间、吞吐量）、安全性、可用性、可伸缩性等方面的具体要求。这些是驱动架构选择的关键。

5. 重要输出示例：

    1. 用例图：展示用户与系统之间的主要交互。

    2. 关键用例描述：详细说明用例的流程、参与者、前置条件、后置条件和异常流。

    3. 用户故事列表：简短、以用户为中心的描述，如“作为安全分析师，我希望能够实时查看异常登录告警，以便快速响应。”

    4. 非功能性需求列表：如“系统应在5秒内生成异常登录告警”。

## 设计逻辑视图
1. 目的：描述系统为最终用户提供的功能抽象和结构。它关注系统的功能分解，将系统划分为更小的、有意义的抽象（类、对象、功能模块），并定义它们之间的关系。

2. 关注点：功能性需求、核心业务概念、模块职责、接口定义。

3. 如何基于场景视图：

    1. 从场景视图中识别出的用例和用户故事，是逻辑视图功能分解的输入。

    2. “威胁检测”用例可能对应“UEBA分析模块”、“告警管理模块”。

    3. “流量监控”用例可能对应“数据采集模块”、“协议解析模块”。

4. 应该做什么：

    1. 识别核心抽象/概念：从业务领域中识别主要实体（如NDR中的“用户”、“设备”、“会话”、“行为事件”、“告警”）。

    2. 进行功能分解：将系统分解为高内聚、低耦合的功能模块或组件。

    3. 定义模块职责：明确每个模块负责哪些功能。

    4. 定义模块间接口：描述模块之间如何进行功能调用和数据交换。

    5. 选择架构风格：考虑采用分层架构、领域驱动设计（DDD）等风格。

5. 重要输出示例：

    1. 高层逻辑组件图：展示主要功能模块及其关系。

    2. 核心领域模型图（或概念性E-R图）：展示业务实体及其关系。

    3. 模块接口定义：描述模块对外提供的API。

## 设计进程视图
1. 目的：描述系统的并发性、运行时性能、分布式特性以及进程/服务之间的通信。它关注系统“如何运行”，特别是在多任务、多用户和分布式环境下的行为。

2. 关注点：非功能性需求（性能、吞吐量、并发、可靠性）、分布式部署、通信机制。

3. 如何基于逻辑视图和场景视图：

    1. 逻辑视图的功能模块在运行时可能对应一个或多个进程/服务。

    2. 场景视图中的性能、实时性、并发用户数等NFRs，是进程视图设计的关键驱动。例如，如果要求实时告警，就需要考虑流处理进程。

4. 应该做什么：

    1. 识别主要进程/服务：将逻辑模块映射到具体的运行时进程或微服务。

    2. 定义进程间通信机制：选择合适的通信方式（如同步的RESTful API、异步的消息队列Kafka、高性能的gRPC）。

    3. 考虑并发和同步：如何处理并发请求？是否需要锁或分布式事务？

    4. 规划数据流和处理管道：设计数据在不同进程/服务之间流动的路径，例如NDR中的实时流量处理管道。

    5. 考虑容错和恢复：进程崩溃后如何恢复？数据如何保证不丢失？

5. 重要输出示例：

    1. 进程通信图：展示主要进程/服务及其通信方式。

    2. 数据流管道图：描绘数据在不同处理阶段的流转。

    3. 并发模型描述：说明如何处理并发请求。

## 设计开发视图
1. 目的：描述系统在开发环境中的组织结构，包括代码的组织、模块化、依赖关系以及开发人员的工作方式。它关注“如何构建系统”。

2. 关注点：可维护性、可重用性、构建效率、团队协作。

3. 如何基于逻辑视图和进程视图：

    1. 逻辑视图的功能分解和进程视图的服务划分，直接影响代码库的组织。

    2. 例如，每个微服务可能对应一个独立的Git仓库或一个大的单体仓库中的一个子目录。

4. 应该做什么：

    1. 定义代码库结构：如何组织源代码、测试代码、配置文件、文档等。

    2. 划分可部署单元（CSCI）：明确哪些部分可以独立编译、测试和部署。

    3. 管理模块依赖：明确模块之间的编译时依赖关系，避免循环依赖。

    4. 制定编码规范和开发标准：确保代码质量和一致性。

    5. 规划构建和测试流程：如何自动化编译、测试和打包。

5. 重要输出示例：

    1. 模块/CSCI组织图：展示代码库的物理结构。

    2. 构建系统配置：如Maven/Gradle/Go Modules配置。

    3. 代码规范文档。

## 设计物理视图
1. 目的：描述系统组件如何部署到实际的硬件或虚拟化基础设施上，以及物理网络拓扑。它关注“如何部署系统”。

2. 关注点：性能、可伸缩性、可靠性、安全性、成本、运维。

3. 如何基于进程视图和非功能性需求：

    1. 进程视图中定义的进程/服务，需要映射到具体的物理或虚拟节点上。

    2. 场景视图和进程视图中的性能、可靠性、安全性等NFRs，是物理部署的关键驱动。例如，高可用性要求多节点部署和负载均衡。

4. 您应该做什么：

    1. 选择部署环境：公有云（阿里云、AWS）、私有云、混合云或本地数据中心。

    2. 规划硬件/虚拟资源：确定所需的服务器（CPU、内存、存储）、网络设备、带宽等。

    3. 设计网络拓扑：包括VPC、子网、安全组、防火墙规则、负载均衡器等。

    4. 映射软件到硬件：将进程/服务部署到具体的节点上。

    5. 考虑灾备和容灾：多区域、多可用区部署策略。

    6. 规划基础设施自动化：使用IaC（Infrastructure as Code）工具。

5. 重要输出示例：

    1. 部署图：展示软件组件到硬件节点的映射。

    2. 网络拓扑图：展示网络连接和安全区域。

    3. 基础设施清单：所需服务器、存储、网络资源列表。

# 关键成功因素
1. 不断验证迭代
2. 持续沟通
3. 文档化，清晰、简洁地记录架构决策和各个视图，但避免过度文档化
4. 工具支持，利用UML工具、绘图工具（如Draw.io, Lucidchart）、架构设计工具等来辅助建模和可视化。
5. 领域知识，深入理解业务领域，才能设计出真正满足业务需求的架构
6. 非功能性需求驱动，NFRs是架构设计的灵魂，它们往往决定了架构的形态