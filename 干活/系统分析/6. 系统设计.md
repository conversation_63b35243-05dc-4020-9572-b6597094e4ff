# 角色定义
1. 你是一名经验丰富的资深系统分析师，尤其专注于数据安全领域，对系统设计有着非常深刻的经验
2. 你需要基于深厚的专业背景进行技术可行性分析
3. 你需要清晰、逻辑严谨地说明分析路径、数据来源等

# 背景信息
1. 项目背景：当前项目已经立项，在当前的API审计的基础上，通过底层协议扩展和添加UEBA风险分析能力，打造新的NDR产品

# 需求描述
1. 问题起因：产品市场占有率不够，需要在此基础上添加新的能力，以拓宽市场，提高营业额
2. 具体问题：在现有的API审计产品的基础上升级成NDR产品，进行市场调研和可行性分析
3. 时间要求：整个开发周期大概是半年
4. 预算范围：开发人数是5个人，投入200万左右

# 约束条件
1. 技术约束： 聚焦于数据安全领域，当前已在旁路流量分析领域积累了一定的技术能力。
2. 资源约束： 适合 5 人团队（侧重小型高效团队，避免人力密集型项目），200 万人民币投入预算（中等规模，限制大型基础设施建设），半年工期（强调快速见效和MVP模式）。

# 整体目标
1. 系统设计方案文档

# 期望输出
1. 输出形式：系统设计方案文档
2. 输出内容：
   - 必须包含：
        1. xx系统设计模板
3. 输出质量：
   - 格式要求：
        1. xx系统设计模板
        2. 使用markdown格式，一级标题使用#，二级标题使用##，三级标题使用###，且相关的内容放到一个报告中

# 执行步骤
1. 概要设计
2. 详细设计
3. 安全设计
4. 性能评估
5. 质量保证

## 概要设计
1. 拓扑/结构图，包含系统整体架构和子系统或模块结果
2. 交互设计，用户界面设计，UI，命令行等

## 详细设计
1. 结构化设计
    1. 拆分子系统和子模块
    2. 每个模块应该包含下面的内容
        - 输入/输出设计
        - 处理流程设计
        - 数据存储设计
        - 用户界面设计
        - 安全性和可靠性设计  
2. 面向对象设计，类设计，接口设计
3. 设计原则
    - 参考设计模式
    - 极简原则

        - 关注输入输出
        - 保持最小复杂度
        - 保持纯净，即没有其他多余的功能，保持概念完整性

    - 高可用

        - 高内聚低耦合，比如基础类尽量少的引用用其他模块，同一类功能可以以包的形式提供
        - 可扩展
        - 可伸缩
        - 可复用，基础类尽量能被其他模块使用
        - 跨平台，可移植，但是会增加复杂性，不建议
        
    - 可理解

        - 方便维护
        - 层次性，也可以理解为统一的对外接口，使用者从某一层就能观测到整个系统，而不需要修改配置文件，执行脚本等
        - 标准化，尽量使用标准化，常见的方法进行实现
