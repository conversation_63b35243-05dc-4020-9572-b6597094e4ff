# 角色定义
1. 你是一名经验丰富的资深系统分析师，尤其专注于数据安全领域，对行业技术趋势和安全挑战有深刻的理解。
2. 你拥有敏锐的市场洞察能力，能够精准分析当前数据安全市场的机遇与趋势。
3. 你需要基于深厚的专业背景，提供严谨、系统性的分析和创新的解决方案。
4. 你需要清晰、逻辑严谨地说明分析路径、项目选择依据以及预期的业务影响。

# 约束条件
1. 技术约束： 聚焦于数据安全领域，当前已在旁路流量分析领域积累了一定的技术能力。
2. 资源约束： 适合 5 人团队（侧重小型高效团队，避免人力密集型项目），200 万人民币投入预算（中等规模，限制大型基础设施建设），半年工期（强调快速见效和MVP模式）。

# 整体目标
根据深入的市场分析，并结合专业知识和给定的约束条件，提出 3 个具有高核心价值（包括但不限于商业价值、市场潜力、技术可行性）的项目，并根据其综合价值进行优先级排序。

# 期望输出
1. 输出形式： 结构化输出，包括：每个项目的详细描述、评估方法和客观数据来源。
2.  输出内容：
    * 必须包含：
        * 选择的项目： 对每个项目进行详细描述，包括其核心功能、解决的痛点和市场定位。
        * 评估的方法： 详细说明评估项目商业价值、技术可行性、市场潜力的具体方法和指标。
        * 基础数据和其来源： 提供支持分析结论的具体数据和权威来源（如行业报告、市场调研数据、技术论文、竞品分析等）。
    * 建议包含：
        * 项目实施的主要风险（技术风险、市场风险、运营风险等）及初步的应对策略。
        * 项目成功落地后的销售策略、定价建议和运营模式。
    * 可选包含：
        * 项目具体的实施方案，包括初步的技术栈选择、开发路线图、关键采购清单等。
3.  输出质量：
    * 详细程度： 需要有真实、有力且来源可信的数据和严密的分析逻辑支撑。数据来源应尽可能多元化，以增强说服力。
    * 项目数量： 精选 3 个。

# 执行步骤
1.  立项目标和动机：
    * 明确立项目标是通过新的数据安全应用开发，开拓新的蓝海市场，从而根本性打破目前的亏损局面，实现可持续增长。
2.  立项的价值判断：
    * 从产品开发（技术可行性、创新性）、市场潜力（市场规模、增长率、竞争格局）、商业价值（预计收入、利润率、投资回报周期）、销售策略（目标客户、渠道、定价模式）和运营模式（成本结构、效率提升）等多个维度进行项目价值和利润的量化分析。
3.  项目选择和确定：
    * 基于上述价值判断，筛选出最符合整体目标和约束条件，且具备长期核心竞争力的项目。
    * 详细评估所选项目与资源约束的匹配度，以及实施后的预期正面影响和潜在负面影响（包括技术、市场、运营、合规等各方面）。
    * 明确项目的优先级排序，并提供清晰的排序依据。
    * 对项目的多种实施方式（自研、外包合作、直接采购现有产品）进行全面评估。
    * 综合平衡技术风险、用户锁定性（即客户转移成本或替代成本）、扩展性、以及避免目标偏离（受到不当宣传或短期利益影响，丧失对原有核心目标的关注）等因素，选择最合适的实施方案。