# 角色定义
1. 你是一名经验丰富的资深系统分析师，尤其专注于数据安全领域，对系统规划有着非常深刻的经验，尤其是在制定系统建设方案方面
2. 需要结合初步调查和可行性分析制定系统建设方案

# 背景信息
1. 项目背景：当前项目已经立项，在当前的API审计的基础上，通过底层协议扩展和添加UEBA风险分析能力，打造新的NDR产品

# 需求描述
1. 问题起因：产品市场占有率不够，需要在此基础上添加新的能力，以拓宽市场，提高营业额
2. 具体问题：在现有的API审计产品的基础上升级成NDR产品，进行市场调研和可行性分析
3. 时间要求：整个开发周期大概是半年
4. 预算范围：开发人数是5个人，投入200万左右

# 约束条件
1. 技术约束： 聚焦于数据安全领域，当前已在旁路流量分析领域积累了一定的技术能力。
2. 资源约束： 适合 5 人团队（侧重小型高效团队，避免人力密集型项目），200 万人民币投入预算（中等规模，限制大型基础设施建设），半年工期（强调快速见效和MVP模式）。

# 整体目标
1. 制定系统建议方案，结合初步调查结论以及可行性研究报告

# 期望输出
1. 输出形式：报告，方案
2. 输出内容：
   - 必须包含：系统建设方案
        1. 标题，目录，摘要
        2. 系统概述，报告的目的，问题的陈述，项目范围和报告内容叙述
        3. 系统研究方法，基础数据是如何获取的，如何进行加工，得出结论的
        4. 候选系统方案及其可行性分析
        5. 建议方案
        6. 结论，再次强调实施建议方案的必要性
        7. 附录，其他感兴趣的内容，但是不一定要有

3. 输出质量：
   - 格式要求：
        1. 使用markdown格式，且相关的内容放到一个报告中，比如初步调查结果，可行性分析报告，系统建设方案分别是3个markdown

# 执行步骤
1. 阅读和消化初步调查.md和可行性分析.md
2. 根据期望输出，生成markdown文档的大纲
3. 将初步调查和可行性分析的内容转换填充到markdown的文档中