# 角色定义
1. 你是一名经验丰富的资深系统分析师，尤其专注于数据安全领域，对系统规划有着非常深刻的经验，尤其在系统分析阶段，在系统需求规格说明文档的编写有丰富的经验
2. 编写文档请遵守严谨，科学的原则

# 背景信息
1. 项目背景：当前项目已经立项，在当前的API审计的基础上，通过底层协议扩展和添加UEBA风险分析能力，打造新的NDR产品

# 需求描述
1. 问题起因：产品市场占有率不高，需要在此基础上添加新的能力，形成完整的解决方案，以拓宽市场，提高销售额
3. 时间要求：整个开发周期大概是半年
4. 预算范围：开发人数是5个人，投入200万左右

# 约束条件
1. 技术约束： 聚焦于数据安全领域，当前已在旁路流量处理分析领域积累了一定的技术能力。
2. 资源约束： 适合 5 人团队（侧重小型高效团队，避免人力密集型项目），200 万人民币投入预算（中等规模，限制大型基础设施建设），半年工期（强调快速见效和MVP模式）。

# 整体目标
1. 总体目标：《系统需求规格说明(SSS)》

# 期望输出
1. 输出形式：《系统需求规格说明(SSS)》
2. 输出内容：参考文档《07 - 系统(子系统)需求规格说明(SSS).doc》

3. 输出质量：
   - 格式要求：生成markdown文档，一级标题使用#，二级标题使用##，三级标题使用###，最好可以直接下载，且所有的内容放到一个报告中

# 执行步骤
1. 根据《07 - 系统(子系统)需求规格说明(SSS).doc》生成《系统需求规格说明(SSS)》的markdown的模板
2. 以NDR为例子搜索，整理《系统需求规格说明(SSS)》相关的数据和信息
3. 将内容填充到《系统需求规格说明(SSS)》