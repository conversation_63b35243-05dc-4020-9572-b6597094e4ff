# 角色定义
1. 你是一名协议解析专家，在数据库协议解析领域有非常强的专业知识，尤其擅长从JDBC驱动转换协议解析能力到C/C++项目中
2. 你需要基于专业背景,提供系统性的分析和解决方案
3. 你需要主动说明分析路径、选择依据与预期影响

# 背景信息
1. 问题背景：需要将Postgre JDBC驱动的协议解析能力迁移到gw-hw项目中
2. 相关环境：
   - 开发环境：macOS
   - 运行环境：Linux
   - 开发语言：C++
   - 项目类型：旁路流量解析系统
3. 现有条件：
   - 目标项目：当前项目中的gw-hw
   - 参考项目：当前项目中的db-derby (Postgre JDBC驱动)
   - 参考实现：http_parser (项目现有HTTP解析器)，User Guidelines中也有关于插件设计关键方法的描述，一定要按照User Guidelines的提示进行开发

# 需求描述
1. 问题起因：需要将Postgre JDBC驱动的协议解析能力迁移到gw-hw项目中
2. 具体问题：
   - 完整迁移所有协议解析功能
   - 保持与现有项目架构的一致性
   - 确保代码质量和可维护性
   - 严格遵循http_parser的实现模式
3. 时间要求：无明确时间限制
4. 预算范围：无明确预算限制

# 约束条件
1. 技术约束：
   - 严格基于本地db-derby代码
   - 严格遵循http_parser的代码结构
   - 符合C++编码规范
   - 保持代码风格一致
2. 资源约束：
   - 基于现有代码库
   - 不引入新的依赖
3. 质量要求：
   - 完整的错误处理
   - 清晰的代码注释
   - 完整的文档说明
4. 其他限制：
   - 内存使用合理
   - 避免资源泄漏

# 整体目标
1. 总体目标：实现Postgre协议解析能力的完整迁移
2. 具体目标：
   - 协议结构支持：完整支持Postgre消息类型定义、数据结构、版本兼容性
   - 交互场景支持：支持事务处理、认证会话、查询结果、存储过程等
   - 代码质量目标：结构清晰、错误处理完善、文档完整、易于维护
   - 架构一致性：与http_parser保持一致的架构设计
3. 验收标准：
   - 功能完整性检查通过
   - 代码质量检查通过
   - 文档完整性检查通过
   - 架构一致性检查通过

# 执行步骤
## 1. 调研阶段
1. 源码分析
   - 分析db-derby项目结构
   - 分析http_parser项目结构
   - 识别协议解析相关代码
   - 提取协议定义和常量
   - 分析协议交互流程

2. 技术调研
   - 分析现有协议解析实现
   - 评估技术可行性
   - 识别潜在风险
   - 确定技术选型
   - 制定技术方案

## 2. 设计阶段
1. 架构设计
   - 基于http_parser的架构模式设计
   - 定义模块划分和职责
   - 设计模块间接口
   - 确定数据流转路径
   - 设计扩展机制

2. 详细设计
   - 类层次结构设计
   - 数据结构设计
   - 接口规范定义
   - 错误处理机制设计
   - 性能优化设计

## 3. 开发阶段
1. 基础框架开发
   - 实现基础类结构
   - 实现核心接口
   - 实现会话管理
   - 实现数据流处理
   - 实现基础工具类

2. 功能模块开发
   - 实现协议解析核心功能
   - 实现消息处理机制
   - 实现事务处理逻辑
   - 实现错误处理机制
   - 实现日志记录功能

3. 代码优化
   - 性能优化
   - 内存使用优化
   - 代码结构优化
   - 注释完善
   - 代码重构

## 4. 审查阶段
1. 代码审查
   - 代码规范检查
   - 代码质量检查
   - 性能问题检查
   - 安全隐患检查
   - 文档完整性检查

2. 架构审查
   - 架构一致性检查
   - 扩展性评估
   - 可维护性评估
   - 性能评估
   - 风险评估

# 评价标准
1. 完整性：是否覆盖所有协议特性和交互场景
2. 可行性：是否满足所有约束条件
3. 性价比：是否在现有资源下实现最优方案
4. 可维护性：代码结构是否清晰，文档是否完整
5. 一致性：是否与http_parser保持一致的架构设计

# 补充说明
1. 特殊情况处理：
   - 自动处理异常情况
   - 自动补充遗漏功能
   - 自动优化代码质量
2. 风险提示：
   - 协议兼容性风险
   - 性能影响风险
   - 代码质量风险
   - 架构一致性风险
3. 后续建议：
   - 持续优化建议
   - 扩展功能建议
   - 维护建议