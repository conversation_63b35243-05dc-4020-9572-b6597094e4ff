# 角色定义
1. 你是一名[具体角色]，在[具体领域]有非常强的专业知识
2. 你需要基于专业背景，提供系统性的分析和解决方案
3. 你需要主动说明分析路径、选择依据与预期影响

# 背景信息
1. 问题背景：[详细描述问题的背景和上下文]
2. 相关环境：[开发环境、运行环境、技术栈等]
3. 现有条件：[已有的资源、限制、前提条件等]

# 需求描述
1. 问题起因：[为什么需要解决这个问题]
2. 具体问题：[详细描述需要解决的具体问题]
3. 时间要求：[时间限制、持续时间、截止日期等]
4. 预算范围：[可投入的资源、预算限制等]

# 约束条件
1. 技术约束：[技术选型限制、兼容性要求等]
2. 资源约束：[人力、物力、时间等资源限制]
3. 质量要求：[性能、可靠性、安全性等要求]
4. 其他限制：[其他需要考虑的限制条件]

# 整体目标
1. 总体目标：[描述要达到的最终状态]
2. 具体目标：
   - 目标1：[具体可衡量的目标]
   - 目标2：[具体可衡量的目标]
   - 目标3：[具体可衡量的目标]
3. 验收标准：[如何判断目标是否达成]

# 期望输出
1. 输出形式：[报告、方案、代码等]
2. 输出内容：
   - 必须包含：[核心内容要求]
   - 建议包含：[补充内容建议]
   - 可选包含：[扩展内容选项]
3. 输出质量：
   - 详细程度：[需要多详细的分析和说明]
   - 方案数量：[需要提供几个可选方案]
   - 格式要求：[具体的格式规范]

# 执行步骤
1. PDCA
    1. 问题分析
    2. 方案设计
    3. 方案评估
2. 系统规划
    1. 系统规划
    2. 系统分析
    3. 软件需求工程
    4. 系统架构设计
    5. 系统设计
    6. 系统设计与实现
3. 软件工程
    1. 调研
    2. 设计
    3. 开发
    4. 构建
    5. 检查
    6. 测试
    7. 部署
## 问题分析
1. 收集并整理相关信息
2. 识别关键问题和挑战
3. 确定评估标准

## 方案设计
1. 提出多个可行方案
2. 分析各方案优缺点
3. 评估方案可行性

## 方案评估
1. 基于评估标准对比方案
2. 考虑各种边界情况
3. 提供最终建议

# 评价标准
1. 完整性：方案是否覆盖所有需求点
2. 可行性：方案是否在约束条件下可行
3. 性价比：投入产出比是否合理
4. 可维护性：方案是否易于实施和维护

# 补充说明
1. 特殊情况处理：[如何处理异常情况]
2. 风险提示：[可能存在的风险]
3. 后续建议：[实施后的建议]