# 代码索引生成提示词模板

## 核心提示词

```
你是一个专业的代码分析师，请为当前项目生成一份详细的代码索引。

### 任务目标
分析项目中的所有代码文件，生成一个结构化的代码索引，帮助开发者快速理解项目架构和定位代码。

### 分析要求

#### 1. 项目概览
- 识别项目类型（Web应用、API服务、库/框架、CLI工具等）
- 确定主要技术栈和编程语言
- 分析项目规模和复杂度
- 识别构建工具和依赖管理

#### 2. 目录结构分析
- 分析每个主要目录的用途
- 识别代码组织模式（按功能、按层次、按模块等）
- 标注重要的配置目录和文件
- 识别资源文件和静态资源

#### 3. 核心文件识别
- 入口文件和启动文件
- 配置文件和环境设置
- 核心业务逻辑文件
- 工具类和公共模块
- 测试文件和测试配置

#### 4. 模块和组件分析
- 识别主要的功能模块
- 分析组件的层次结构
- 理解模块间的依赖关系
- 识别共享组件和工具函数

#### 5. API和接口分析
- 识别API端点和路由
- 分析数据模型和接口定义
- 理解服务间的通信方式
- 识别外部API集成

#### 6. 数据流分析
- 理解数据的流向和处理
- 识别状态管理方案
- 分析数据存储和持久化
- 理解缓存和性能优化

### 输出格式要求

请按照以下Markdown格式生成代码索引，并确保内容保存到项目根目录的 `CONTEXT.md` 文件中：

```markdown
# 项目代码索引

## 项目概览
- **项目名称**: [项目名称]
- **项目类型**: [项目类型]
- **主要技术栈**: [技术栈列表]
- **开发语言**: [编程语言]
- **构建工具**: [构建工具]
- **包管理器**: [包管理器]
- **最后更新**: [当前日期]

## 项目统计
- **总文件数**: [数量]
- **代码文件数**: [数量]
- **测试文件数**: [数量]
- **配置文件数**: [数量]
- **代码行数**: [估算]

## 目录结构
```
[项目目录树，标注每个目录的用途]
```

## 核心文件索引

### 入口文件
- **[文件路径]**: [文件描述和主要功能]

### 配置文件
- **[文件路径]**: [配置用途和重要设置]

### 核心模块
- **[模块路径]**: [模块功能和职责]

### 工具类
- **[文件路径]**: [工具类功能和使用场景]

## 功能模块索引

### [模块名称1]
- **位置**: [模块路径]
- **功能**: [模块主要功能]
- **关键文件**:
  - `[文件名]`: [文件功能]
- **依赖关系**: [依赖的其他模块]
- **对外接口**: [提供的API或接口]

### [模块名称2]
[同上格式]

## API接口索引

### REST API端点
- **GET /api/[endpoint]**: [接口功能描述]
- **POST /api/[endpoint]**: [接口功能描述]

### GraphQL接口
- **[Query/Mutation名称]**: [接口功能描述]

### 内部API
- **[函数/方法名]**: [功能描述和参数]

## 数据模型索引

### 数据库模型
- **[模型名称]**: [模型用途和字段说明]

### 接口类型
- **[类型名称]**: [类型定义和使用场景]

### 状态管理
- **[状态名称]**: [状态用途和数据结构]

## 组件索引（前端项目）

### 页面组件
- **[组件名称]**: [组件功能和路由]

### 通用组件
- **[组件名称]**: [组件功能和使用场景]

### 业务组件
- **[组件名称]**: [组件功能和业务逻辑]

## 服务索引（后端项目）

### 业务服务
- **[服务名称]**: [服务功能和职责]

### 数据服务
- **[服务名称]**: [数据操作和接口]

### 工具服务
- **[服务名称]**: [工具功能和使用方式]

## 测试索引

### 单元测试
- **[测试文件]**: [测试覆盖的功能]

### 集成测试
- **[测试文件]**: [测试场景和覆盖范围]

### E2E测试
- **[测试文件]**: [测试流程和验证点]

## 配置和环境

### 环境配置
- **开发环境**: [配置文件和设置]
- **测试环境**: [配置文件和设置]
- **生产环境**: [配置文件和设置]

### 构建配置
- **[配置文件]**: [构建规则和优化设置]

### 部署配置
- **[配置文件]**: [部署脚本和环境设置]

## 依赖关系图

### 核心依赖
```
[模块A] --> [模块B]
[模块B] --> [模块C]
```

### 外部依赖
- **[依赖包名]**: [用途和版本]

## 开发指南

### 新功能开发
1. [开发流程步骤]

### 代码规范
- [编码规范要点]

### 测试要求
- [测试覆盖要求]

## 常见问题和解决方案

### 开发环境问题
- **问题**: [问题描述]
- **解决方案**: [解决步骤]

### 部署问题
- **问题**: [问题描述]
- **解决方案**: [解决步骤]

## 更新日志
- **[日期]**: [更新内容]
```

### 特别要求
1. **准确性**: 确保所有路径和文件名准确无误
2. **完整性**: 覆盖项目中的所有重要文件和模块
3. **实用性**: 提供足够的细节帮助开发者快速定位代码
4. **可维护性**: 使用清晰的结构便于后续更新
5. **导航性**: 添加适当的链接和交叉引用

请基于提供的项目文件和目录结构，生成符合上述要求的详细代码索引。
```

## 使用方法

### 方法1: 直接使用Gemini CLI
```bash
# 生成项目代码索引
gemini -f "9. 代码索引生成提示词.md" \
       -i <(find . -type f -name "*.js" -o -name "*.py" -o -name "*.java" -o -name "*.ts" | head -50 | xargs ls -la) \
       -i <(tree -L 4 -I 'node_modules|.git|dist|build' 2>/dev/null || find . -type d | head -30) \
       "请为当前项目生成详细的代码索引" > CONTEXT.md
```

### 方法2: 集成到助手脚本
在 `gemini-helper.sh` 中添加新命令：
```bash
# 生成代码索引
generate_index() {
    log_info "生成项目代码索引..."
    
    # 收集项目信息
    local project_info="/tmp/project_analysis.txt"
    {
        echo "=== 项目基本信息 ==="
        echo "项目路径: $(pwd)"
        echo "项目名称: $(basename $(pwd))"
        echo "创建时间: $(date)"
        echo ""
        
        echo "=== 目录结构 ==="
        tree -L 4 -I 'node_modules|.git|dist|build|__pycache__|.pytest_cache' 2>/dev/null || \
        find . -type d -not -path './node_modules*' -not -path './.git*' | head -30
        echo ""
        
        echo "=== 代码文件列表 ==="
        find . -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -o -name "*.py" -o -name "*.java" -o -name "*.go" -o -name "*.rs" \) \
             -not -path './node_modules/*' -not -path './.git/*' -not -path './dist/*' -not -path './build/*' | \
             head -100 | while read file; do
            echo "文件: $file ($(wc -l < "$file" 2>/dev/null || echo 0) 行)"
        done
        echo ""
        
        echo "=== 配置文件 ==="
        find . -maxdepth 3 -name "*.json" -o -name "*.yaml" -o -name "*.yml" -o -name "*.toml" -o -name "*.ini" | \
             head -20
        echo ""
        
        echo "=== 包管理文件 ==="
        ls -la package.json requirements.txt Cargo.toml pom.xml go.mod 2>/dev/null || echo "未找到标准包管理文件"
        echo ""
        
        echo "=== Git信息 ==="
        if [ -d ".git" ]; then
            echo "当前分支: $(git branch --show-current 2>/dev/null || echo 'unknown')"
            echo "最近提交: $(git log -1 --oneline 2>/dev/null || echo 'no commits')"
            echo "文件统计: $(git ls-files | wc -l) 个文件在版本控制中"
        else
            echo "未使用Git版本控制"
        fi
    } > "$project_info"
    
    # 生成代码索引
    local prompt_file="$TEMPLATES_DIR/9. 代码索引生成提示词.md"
    
    gemini -f "$prompt_file" \
           -i "$project_info" \
           -i "$CONTEXT_FILE" \
           "请基于提供的项目信息生成详细的代码索引，保存到CONTEXT.md文件中" > CONTEXT.md
    
    rm -f "$project_info"
    
    if [ -f "CONTEXT.md" ]; then
        log_success "代码索引已生成: CONTEXT.md"
        echo "📊 索引统计:"
        echo "- 总行数: $(wc -l < CONTEXT.md)"
        echo "- 文件大小: $(du -h CONTEXT.md | cut -f1)"
    else
        log_error "代码索引生成失败"
        return 1
    fi
}
```

### 方法3: 自动化脚本
创建独立的索引生成脚本 `generate-index.sh`：
```bash
#!/bin/bash

TEMPLATES_DIR="模板-geminicli"
OUTPUT_FILE="CONTEXT.md"

echo "🔍 正在分析项目结构..."

# 生成项目分析数据
{
    echo "项目分析数据"
    echo "============="
    echo "分析时间: $(date)"
    echo "项目路径: $(pwd)"
    echo ""
    
    echo "目录结构:"
    tree -L 3 -I 'node_modules|.git|dist|build' 2>/dev/null || find . -type d | head -20
    echo ""
    
    echo "代码文件统计:"
    find . -name "*.js" -o -name "*.py" -o -name "*.java" -o -name "*.ts" | wc -l | xargs echo "代码文件数:"
    find . -name "*.test.js" -o -name "*_test.py" -o -name "*Test.java" | wc -l | xargs echo "测试文件数:"
    echo ""
    
    echo "主要文件列表:"
    find . -type f \( -name "*.js" -o -name "*.py" -o -name "*.java" -o -name "*.ts" \) \
         -not -path './node_modules/*' -not -path './.git/*' | head -50
} | gemini -f "$TEMPLATES_DIR/9. 代码索引生成提示词.md" \
           "请生成项目代码索引" > "$OUTPUT_FILE"

echo "✅ 代码索引已生成: $OUTPUT_FILE"
```

## 高级功能

### 增量更新索引
```bash
# 检查文件变更并更新索引
update_index() {
    if [ -f "CONTEXT.md" ]; then
        local last_update=$(stat -c %Y CONTEXT.md 2>/dev/null || stat -f %m CONTEXT.md 2>/dev/null)
        local recent_changes=$(find . -newer CONTEXT.md -name "*.js" -o -name "*.py" -o -name "*.java" -o -name "*.ts" 2>/dev/null | wc -l)
        
        if [ "$recent_changes" -gt 0 ]; then
            echo "发现 $recent_changes 个文件有更新，重新生成索引..."
            generate_index
        else
            echo "代码索引是最新的，无需更新"
        fi
    else
        echo "未找到现有索引，生成新索引..."
        generate_index
    fi
}
```

### 多语言项目支持
```bash
# 根据项目类型定制索引生成
detect_project_type() {
    if [ -f "package.json" ]; then
        echo "nodejs"
    elif [ -f "requirements.txt" ] || [ -f "setup.py" ]; then
        echo "python"
    elif [ -f "pom.xml" ]; then
        echo "java"
    elif [ -f "Cargo.toml" ]; then
        echo "rust"
    elif [ -f "go.mod" ]; then
        echo "go"
    else
        echo "unknown"
    fi
}
```

这个提示词模板将帮助你生成一个全面的项目代码索引，让团队成员能够快速理解项目结构和定位代码！
