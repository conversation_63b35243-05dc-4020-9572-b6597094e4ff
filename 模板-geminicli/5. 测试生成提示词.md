# 测试生成提示词模板

## 单元测试生成

```
请为以下代码生成完整的单元测试：

### 目标代码
```{language}
{代码片段}
```

### 测试要求
1. **测试覆盖**
   - 覆盖所有公共方法和函数
   - 测试正常流程和异常情况
   - 包含边界条件和极端情况
   - 达到90%以上的代码覆盖率

2. **测试质量**
   - 使用清晰的测试命名
   - 遵循AAA模式（Arrange-Act-Assert）
   - 每个测试只验证一个行为
   - 测试应该独立且可重复

3. **测试框架**
   - 使用项目中已有的测试框架
   - 遵循项目的测试约定
   - 使用合适的断言和匹配器
   - 包含必要的测试工具和模拟

4. **测试数据**
   - 提供多样化的测试数据
   - 包含有效和无效的输入
   - 考虑不同的数据类型和格式
   - 使用测试数据构建器模式

请生成完整的测试代码，包括测试类、测试方法和必要的辅助代码。
```

## 集成测试生成

```
请为以下模块生成集成测试：

### 测试目标
模块：{模块名称}
功能：{功能描述}
依赖：{依赖模块}

### 集成测试要求
1. **测试范围**
   - 测试模块间的交互
   - 验证数据流的正确性
   - 检查接口契约的遵守
   - 测试配置和环境依赖

2. **测试场景**
   - 正常业务流程
   - 异常处理流程
   - 并发访问场景
   - 资源限制情况

3. **测试环境**
   - 设置测试数据库
   - 配置外部服务模拟
   - 准备测试环境变量
   - 管理测试资源生命周期

4. **验证点**
   - 功能正确性验证
   - 性能指标检查
   - 错误处理验证
   - 日志和监控验证

请提供完整的集成测试方案和实现代码。
```

## API测试生成

```
请为以下API生成测试用例：

### API信息
端点：{API端点}
方法：{HTTP方法}
参数：{请求参数}
响应：{响应格式}

### API测试要求
1. **功能测试**
   - 正常请求响应测试
   - 参数验证测试
   - 权限和认证测试
   - 业务逻辑验证测试

2. **边界测试**
   - 参数边界值测试
   - 数据长度限制测试
   - 并发请求测试
   - 超时和重试测试

3. **错误测试**
   - 无效参数测试
   - 权限不足测试
   - 服务不可用测试
   - 数据冲突测试

4. **性能测试**
   - 响应时间测试
   - 吞吐量测试
   - 负载测试
   - 压力测试

请生成完整的API测试套件，包括测试数据和验证逻辑。
```

## 端到端测试生成

```
请为以下用户场景生成端到端测试：

### 测试场景
用户故事：{用户故事}
业务流程：{业务流程}
涉及页面：{页面列表}

### E2E测试要求
1. **用户旅程**
   - 模拟真实用户操作
   - 覆盖完整业务流程
   - 包含多个系统交互
   - 验证端到端功能

2. **测试步骤**
   - 清晰的步骤描述
   - 可重复的操作序列
   - 合理的等待和同步
   - 详细的验证点

3. **测试数据**
   - 准备测试用户和权限
   - 设置业务数据状态
   - 管理测试数据生命周期
   - 清理测试环境

4. **测试工具**
   - 使用合适的自动化工具
   - 配置浏览器和环境
   - 实现页面对象模式
   - 添加截图和报告

请提供完整的E2E测试实现和配置。
```

## 性能测试生成

```
请为以下系统生成性能测试：

### 测试目标
系统：{系统名称}
关键功能：{关键功能}
性能要求：{性能指标}

### 性能测试要求
1. **测试类型**
   - 负载测试：正常负载下的性能
   - 压力测试：极限负载下的表现
   - 容量测试：系统容量上限
   - 稳定性测试：长时间运行稳定性

2. **测试指标**
   - 响应时间（平均、最大、百分位）
   - 吞吐量（TPS、QPS）
   - 资源使用率（CPU、内存、磁盘、网络）
   - 错误率和成功率

3. **测试场景**
   - 单用户场景
   - 多用户并发场景
   - 混合业务场景
   - 峰值流量场景

4. **测试环境**
   - 生产环境相似的配置
   - 真实的数据量级
   - 网络延迟模拟
   - 监控和日志收集

请提供完整的性能测试方案和脚本。
```

## 测试数据生成

```
请为以下测试生成测试数据：

### 数据需求
测试类型：{测试类型}
数据模型：{数据模型}
数据量：{数据量要求}

### 测试数据要求
1. **数据多样性**
   - 覆盖各种数据类型
   - 包含正常和异常数据
   - 考虑边界值和特殊值
   - 模拟真实业务数据

2. **数据质量**
   - 数据格式正确
   - 业务逻辑一致
   - 关联关系完整
   - 数据分布合理

3. **数据管理**
   - 数据生成脚本
   - 数据清理机制
   - 数据版本控制
   - 数据隐私保护

4. **数据工具**
   - 使用数据生成库
   - 实现数据构建器
   - 提供数据工厂方法
   - 支持数据参数化

请提供完整的测试数据生成方案和工具。
```

## 测试自动化

```
请为项目设计测试自动化方案：

### 项目信息
项目类型：{项目类型}
技术栈：{技术栈}
测试需求：{测试需求}

### 自动化要求
1. **测试策略**
   - 测试金字塔设计
   - 测试类型分层
   - 测试覆盖率目标
   - 测试执行策略

2. **工具选择**
   - 单元测试框架
   - 集成测试工具
   - UI自动化工具
   - 性能测试工具

3. **CI/CD集成**
   - 测试流水线设计
   - 测试触发条件
   - 测试结果报告
   - 失败处理机制

4. **维护策略**
   - 测试代码组织
   - 测试数据管理
   - 测试环境维护
   - 测试用例更新

请提供完整的测试自动化实施方案。
```

## 使用指南

### 1. 选择合适的测试模板
- **单元测试**: 函数和类级别的测试
- **集成测试**: 模块间交互测试
- **API测试**: 接口功能和性能测试
- **E2E测试**: 完整业务流程测试
- **性能测试**: 系统性能验证
- **测试数据**: 测试数据准备和管理
- **测试自动化**: 整体测试策略和工具

### 2. 自定义测试要求
- 根据项目特点调整测试重点
- 结合业务需求定制测试场景
- 考虑团队技能选择测试工具

### 3. 测试质量保证
- 定期审查测试用例质量
- 监控测试覆盖率和有效性
- 持续优化测试执行效率
- 建立测试最佳实践
