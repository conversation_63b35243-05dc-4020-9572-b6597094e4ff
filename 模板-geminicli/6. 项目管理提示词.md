# 项目管理提示词模板

## 任务分解

```
请将以下需求分解为具体的开发任务：

### 需求描述
{需求详细描述}

### 分解要求
1. **任务层次**
   - 按功能模块分组
   - 区分前端、后端、数据库等不同层次
   - 识别依赖关系和执行顺序
   - 估算每个任务的工作量

2. **任务详情**
   - 任务名称和描述
   - 验收标准和完成定义
   - 技术要求和实现方案
   - 风险点和注意事项

3. **优先级排序**
   - 按业务价值排序
   - 考虑技术依赖关系
   - 识别关键路径
   - 标注里程碑节点

4. **资源评估**
   - 所需技能和人员
   - 开发时间估算
   - 测试和部署时间
   - 文档和培训需求

请提供结构化的任务分解结果，包括甘特图建议。
```

## 进度跟踪

```
请帮我跟踪项目进度：

### 项目信息
项目名称：{项目名称}
开始时间：{开始时间}
计划完成时间：{计划完成时间}
当前状态：{当前状态}

### 跟踪维度
1. **任务完成情况**
   - 已完成任务列表
   - 进行中任务状态
   - 待开始任务计划
   - 阻塞任务和原因

2. **时间进度**
   - 计划vs实际进度对比
   - 关键里程碑达成情况
   - 延期风险评估
   - 时间调整建议

3. **质量指标**
   - 代码质量指标
   - 测试覆盖率
   - Bug数量和严重程度
   - 技术债务情况

4. **资源使用**
   - 人员投入情况
   - 技术资源使用
   - 预算执行情况
   - 外部依赖状态

请提供详细的进度报告和风险预警。
```

## 风险管理

```
请分析项目的潜在风险：

### 项目背景
项目类型：{项目类型}
技术栈：{技术栈}
团队规模：{团队规模}
时间要求：{时间要求}

### 风险识别
1. **技术风险**
   - 新技术学习成本
   - 技术方案可行性
   - 性能和扩展性风险
   - 第三方依赖风险

2. **人员风险**
   - 关键人员依赖
   - 技能匹配度
   - 人员流动风险
   - 沟通协作风险

3. **时间风险**
   - 需求变更风险
   - 技术难度低估
   - 测试时间不足
   - 部署复杂度

4. **业务风险**
   - 需求理解偏差
   - 用户接受度
   - 竞争对手影响
   - 市场环境变化

### 风险应对
- 风险等级评估（高/中/低）
- 影响程度分析
- 应对策略制定
- 监控和预警机制

请提供完整的风险管理方案。
```

## 代码审查管理

```
请制定代码审查流程：

### 项目特点
团队规模：{团队规模}
开发模式：{开发模式}
技术栈：{技术栈}
质量要求：{质量要求}

### 审查流程
1. **审查触发**
   - 提交代码时自动触发
   - 合并请求前必须审查
   - 定期代码质量检查
   - 重要功能专项审查

2. **审查标准**
   - 功能正确性检查
   - 代码规范遵循
   - 性能和安全考虑
   - 测试覆盖要求

3. **审查角色**
   - 主审查员职责
   - 次审查员参与
   - 架构师把关
   - 新人学习机制

4. **审查工具**
   - 代码审查平台选择
   - 自动化检查工具
   - 质量度量工具
   - 知识分享平台

### 质量保证
- 审查清单制定
- 审查质量监控
- 审查效率优化
- 团队能力提升

请提供详细的代码审查实施方案。
```

## 发布管理

```
请制定项目发布管理流程：

### 发布信息
项目类型：{项目类型}
部署环境：{部署环境}
发布频率：{发布频率}
用户规模：{用户规模}

### 发布流程
1. **发布准备**
   - 功能完成度检查
   - 测试通过确认
   - 文档更新完成
   - 发布计划制定

2. **发布执行**
   - 环境准备和验证
   - 数据库迁移执行
   - 应用部署和配置
   - 服务启动和验证

3. **发布验证**
   - 功能验证测试
   - 性能指标检查
   - 监控告警确认
   - 用户反馈收集

4. **发布回滚**
   - 回滚触发条件
   - 回滚执行步骤
   - 数据恢复方案
   - 问题修复流程

### 自动化支持
- CI/CD流水线设计
- 自动化测试集成
- 部署脚本管理
- 监控和告警配置

请提供完整的发布管理方案。
```

## 团队协作

```
请优化团队协作流程：

### 团队情况
团队规模：{团队规模}
技能分布：{技能分布}
工作模式：{工作模式}
协作工具：{协作工具}

### 协作优化
1. **沟通机制**
   - 日常沟通节奏
   - 会议类型和频率
   - 信息同步方式
   - 决策制定流程

2. **工作分配**
   - 任务分配原则
   - 技能匹配策略
   - 负载均衡考虑
   - 成长机会安排

3. **知识管理**
   - 技术文档维护
   - 经验分享机制
   - 培训计划制定
   - 最佳实践总结

4. **工具平台**
   - 项目管理工具
   - 代码协作平台
   - 沟通交流工具
   - 知识库系统

### 效率提升
- 流程标准化
- 工具自动化
- 重复工作消除
- 团队能力建设

请提供团队协作改进方案。
```

## 质量管理

```
请建立项目质量管理体系：

### 质量目标
项目类型：{项目类型}
质量要求：{质量要求}
用户期望：{用户期望}
行业标准：{行业标准}

### 质量体系
1. **质量标准**
   - 代码质量标准
   - 功能质量要求
   - 性能质量指标
   - 安全质量规范

2. **质量控制**
   - 开发阶段质量检查
   - 测试阶段质量验证
   - 发布前质量确认
   - 生产环境质量监控

3. **质量保证**
   - 质量流程制定
   - 质量工具配置
   - 质量培训实施
   - 质量文化建设

4. **质量改进**
   - 质量问题分析
   - 根因分析方法
   - 改进措施制定
   - 效果跟踪验证

### 度量指标
- 缺陷密度和趋势
- 测试覆盖率
- 用户满意度
- 系统可用性

请提供完整的质量管理实施方案。
```

## 使用指南

### 1. 选择合适的管理模板
- **任务分解**: 项目启动和规划阶段
- **进度跟踪**: 项目执行过程中
- **风险管理**: 贯穿项目全生命周期
- **代码审查**: 开发质量控制
- **发布管理**: 部署和上线流程
- **团队协作**: 团队效率优化
- **质量管理**: 质量体系建设

### 2. 自定义管理要求
- 根据项目规模调整管理粒度
- 结合团队特点定制流程
- 考虑组织文化适配管理方式

### 3. 持续改进
- 定期评估管理效果
- 收集团队反馈意见
- 优化管理流程和工具
- 建立管理最佳实践
