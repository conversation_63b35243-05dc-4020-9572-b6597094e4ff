# Gemini CLI 实现 Augment 效果的提示词分析

## 目标
通过精心设计的提示词，让Gemini CLI具备类似Augment的智能代码助手能力。

## Augment核心功能分析

### 1. 代码库上下文理解
- **功能**: 理解整个项目结构、依赖关系、架构模式
- **实现方式**: 通过文件遍历、依赖分析、模式识别

### 2. 智能代码检索
- **功能**: 基于自然语言描述精确找到相关代码片段
- **实现方式**: 语义搜索、代码索引、关联分析

### 3. 精确代码编辑
- **功能**: 理解上下文后进行精确的代码修改
- **实现方式**: 代码解析、影响分析、安全编辑

### 4. 项目管理
- **功能**: 任务分解、进度跟踪、工作流管理
- **实现方式**: 结构化任务管理、状态跟踪

### 5. 测试驱动开发
- **功能**: 自动生成测试、验证代码正确性
- **实现方式**: 测试模式识别、用例生成

## 需要构建的提示词模板

### 1. 系统角色定义提示词
```
你是一个专业的AI代码助手，具备以下核心能力：
- 深度理解代码库结构和业务逻辑
- 基于上下文进行精确的代码分析和修改
- 遵循最佳实践和代码规范
- 提供测试驱动的解决方案
```

### 2. 代码库分析提示词
```
请分析当前代码库：
1. 识别项目类型、技术栈、架构模式
2. 理解模块间的依赖关系
3. 识别核心业务逻辑和数据流
4. 分析代码质量和潜在问题
5. 总结项目的整体结构和设计理念
```

### 3. 智能检索提示词
```
基于以下描述找到相关代码：
描述：{用户描述}

请：
1. 理解用户意图和需求
2. 在代码库中搜索相关文件和函数
3. 分析代码的功能和实现方式
4. 提供精确的代码位置和解释
5. 如果需要修改，给出具体建议
```

### 4. 代码编辑提示词
```
请对以下代码进行修改：
目标：{修改目标}
当前代码：{代码片段}

要求：
1. 保持代码风格一致性
2. 确保修改不破坏现有功能
3. 考虑对其他模块的影响
4. 提供修改前后的对比
5. 解释修改的原因和好处
```

### 5. 测试生成提示词
```
为以下代码生成测试：
代码：{代码片段}

请：
1. 分析代码的功能和边界条件
2. 设计全面的测试用例
3. 包含正常情况和异常情况
4. 使用项目中已有的测试框架
5. 确保测试的可维护性和可读性
```

## 实现策略

### 阶段1：基础能力构建
- [ ] 创建系统角色提示词
- [ ] 实现代码库扫描和分析
- [ ] 建立基础的代码理解能力

### 阶段2：智能检索和编辑
- [ ] 实现语义化代码搜索
- [ ] 构建精确的代码编辑能力
- [ ] 添加影响分析功能

### 阶段3：高级功能
- [ ] 集成测试生成
- [ ] 添加项目管理功能
- [ ] 实现持续学习和优化

## 关键挑战

1. **上下文限制**: Gemini CLI的上下文窗口限制
2. **状态保持**: 缺乏持久化的项目状态管理
3. **工具集成**: 无法直接调用外部工具和API
4. **实时更新**: 无法实时感知代码库变化

## 解决方案

1. **分块处理**: 将大型代码库分块分析
2. **状态文件**: 使用本地文件保存项目状态
3. **脚本集成**: 通过shell脚本桥接外部工具
4. **增量更新**: 设计增量分析机制