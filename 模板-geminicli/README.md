# Gemini CLI 实现 Augment 效果提示词模板库

## 概述

这个模板库旨在帮助你通过精心设计的提示词，让Gemini CLI具备类似Augment的智能代码助手能力。通过分层的提示词架构和系统化的实施方案，实现高效的代码分析、编辑、测试和项目管理功能。

## 文件结构

```
模板-geminicli/
├── 1. geminicli分析.md          # 核心功能分析和实施策略
├── 2. 系统角色提示词.md         # AI助手角色定义和能力描述
├── 3. 代码分析提示词.md         # 项目分析、问题诊断、性能分析
├── 4. 代码编辑提示词.md         # 代码修改、重构、优化、审查
├── 5. 测试生成提示词.md         # 单元测试、集成测试、性能测试
├── 6. 项目管理提示词.md         # 任务分解、进度跟踪、风险管理
├── 7. 实施指南.md              # 具体实施方案和最佳实践
└── README.md                   # 本文档
```

## 核心能力对比

| 功能 | Augment | Gemini CLI + 提示词 | 实现方式 |
|------|---------|-------------------|----------|
| 代码库理解 | ✅ 实时索引 | ✅ 提示词分析 | 分层上下文管理 |
| 智能检索 | ✅ 语义搜索 | ✅ 描述式搜索 | 智能搜索提示词 |
| 精确编辑 | ✅ 上下文感知 | ✅ 模板化编辑 | 结构化编辑提示词 |
| 测试生成 | ✅ 自动生成 | ✅ 模板生成 | 测试模板库 |
| 项目管理 | ✅ 任务跟踪 | ✅ 提示词管理 | 管理提示词模板 |
| 工具集成 | ✅ 原生集成 | ⚠️ 脚本桥接 | Shell脚本封装 |

## 快速开始

### 1. 环境准备
```bash
# 安装Gemini CLI
npm install -g @google/generative-ai-cli

# 配置API密钥
export GEMINI_API_KEY="your-api-key"
```

### 2. 下载模板
```bash
# 克隆或下载提示词模板
git clone <repository-url>
cd 模板-geminicli
```

### 3. 基础使用
```bash
# 使用系统角色提示词
gemini -f "2. 系统角色提示词.md" "请分析这个项目"

# 使用代码分析模板
gemini -f "3. 代码分析提示词.md" "分析src/main.js的功能"

# 使用代码编辑模板
gemini -f "4. 代码编辑提示词.md" "优化这个函数的性能"
```

## 核心特性

### 🎯 分层提示词架构
- **系统层**: 角色定义和工作原则
- **功能层**: 具体功能模板
- **应用层**: 特定场景定制

### 🔍 智能代码分析
- 项目架构理解
- 功能模块分析
- 问题诊断定位
- 性能瓶颈识别
- 安全风险评估

### ✏️ 精确代码编辑
- 上下文感知修改
- 代码风格保持
- 影响范围分析
- 质量保证机制

### 🧪 全面测试支持
- 单元测试生成
- 集成测试设计
- API测试用例
- 性能测试方案
- 测试数据管理

### 📊 项目管理能力
- 需求任务分解
- 进度跟踪监控
- 风险识别管理
- 团队协作优化

## 使用场景

### 场景1: 新项目分析
```bash
# 1. 使用项目分析模板
gemini -f "3. 代码分析提示词.md" "请对当前项目进行全面分析"

# 2. 生成项目文档
gemini -f "6. 项目管理提示词.md" "生成项目开发计划"
```

### 场景2: 代码重构
```bash
# 1. 分析重构目标
gemini -f "3. 代码分析提示词.md" "分析src/legacy.js的重构需求"

# 2. 执行重构
gemini -f "4. 代码编辑提示词.md" "重构这个模块提升可维护性"

# 3. 生成测试
gemini -f "5. 测试生成提示词.md" "为重构后的代码生成测试"
```

### 场景3: 问题排查
```bash
# 1. 问题诊断
gemini -f "3. 代码分析提示词.md" "分析这个性能问题的原因"

# 2. 解决方案
gemini -f "4. 代码编辑提示词.md" "修复这个性能瓶颈"

# 3. 验证测试
gemini -f "5. 测试生成提示词.md" "生成性能测试用例"
```

## 高级功能

### 1. 上下文管理
- 项目上下文文件维护
- 会话状态持久化
- 增量信息更新

### 2. 工具集成
- Git工作流集成
- CI/CD流水线支持
- 代码质量工具对接

### 3. 自动化脚本
- 智能代码搜索
- 自动化重构
- 项目健康检查

## 最佳实践

### 1. 提示词设计原则
- **具体性**: 提供明确的上下文和要求
- **结构化**: 使用清晰的格式和层次
- **可重复**: 确保一致性和可重复性
- **可扩展**: 模块化设计便于扩展

### 2. 使用建议
- 从简单场景开始，逐步扩展
- 建立项目特定的上下文文件
- 定期优化和更新提示词模板
- 结合实际项目需求定制模板

### 3. 质量保证
- 对生成结果进行验证
- 建立测试和审查机制
- 保持人工监督和确认
- 建立回滚和恢复机制

## 限制和解决方案

### 主要限制
1. **上下文窗口限制** → 分块处理和上下文管理
2. **状态持久化缺失** → 本地文件状态管理
3. **工具集成受限** → Shell脚本桥接
4. **实时更新缺失** → 增量分析机制

### 解决策略
- 设计分层的上下文管理系统
- 使用本地文件保存项目状态
- 通过脚本集成外部工具
- 建立增量更新机制

## 扩展方向

### 短期目标
- [ ] 完善基础提示词模板
- [ ] 实现核心脚本工具
- [ ] 建立使用文档和示例

### 中期目标
- [ ] 开发GUI界面
- [ ] 集成更多开发工具
- [ ] 建立模板市场

### 长期目标
- [ ] 实现插件化架构
- [ ] 支持团队协作功能
- [ ] 建立学习和优化机制

## 贡献指南

欢迎贡献新的提示词模板和改进建议：

1. Fork项目仓库
2. 创建功能分支
3. 添加或改进提示词模板
4. 提交Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 参与讨论

---

**开始你的Gemini CLI增强之旅吧！** 🚀
