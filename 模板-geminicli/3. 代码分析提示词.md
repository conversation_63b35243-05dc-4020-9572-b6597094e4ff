# 代码分析提示词模板

## 项目初始化分析

```
请对当前项目进行全面分析，我需要你：

### 1. 项目概览
- 识别项目类型（Web应用、API服务、库/框架、CLI工具等）
- 确定主要技术栈和编程语言
- 分析项目规模和复杂度
- 识别构建工具和依赖管理方式

### 2. 架构分析
- 分析项目的整体架构模式（MVC、微服务、分层架构等）
- 识别核心模块和组件
- 理解数据流和控制流
- 分析模块间的依赖关系

### 3. 代码质量评估
- 检查代码规范和一致性
- 识别潜在的代码异味和技术债务
- 分析测试覆盖率和测试质量
- 评估文档完整性

### 4. 关键文件识别
- 找出配置文件和环境设置
- 识别入口文件和核心业务逻辑
- 定位重要的工具类和公共模块
- 分析第三方依赖和版本情况

请以结构化的方式提供分析结果，包括：
- 项目摘要
- 技术栈清单
- 架构图描述
- 关键文件列表
- 改进建议
```

## 特定功能分析

```
请分析以下功能模块：
功能描述：{功能描述}
相关文件：{文件路径}

分析要求：
1. **功能理解**
   - 该功能的业务目的和价值
   - 输入输出和数据流
   - 与其他模块的交互关系

2. **实现分析**
   - 核心算法和逻辑
   - 使用的设计模式
   - 性能特点和瓶颈

3. **代码质量**
   - 代码结构和可读性
   - 错误处理和边界条件
   - 测试覆盖情况

4. **改进空间**
   - 性能优化机会
   - 代码重构建议
   - 功能扩展可能性

请提供详细的分析报告和具体的改进建议。
```

## 问题诊断分析

```
我遇到了以下问题，请帮我分析：
问题描述：{问题描述}
错误信息：{错误信息}
相关代码：{代码片段}

请进行以下分析：

### 1. 问题定位
- 分析错误信息和堆栈跟踪
- 识别问题的根本原因
- 确定影响范围和严重程度

### 2. 原因分析
- 代码逻辑问题
- 配置或环境问题
- 依赖版本冲突
- 数据或状态问题

### 3. 解决方案
- 提供多种解决方案
- 评估每种方案的优缺点
- 推荐最佳解决方案
- 给出具体的修改步骤

### 4. 预防措施
- 如何避免类似问题
- 相关的测试用例
- 监控和告警建议
- 文档更新需求

请提供详细的诊断报告和解决方案。
```

## 性能分析

```
请对以下代码进行性能分析：
代码：{代码片段}
场景：{使用场景}

分析维度：

### 1. 时间复杂度分析
- 算法的时间复杂度
- 关键操作的执行次数
- 潜在的性能瓶颈点

### 2. 空间复杂度分析
- 内存使用情况
- 数据结构选择的合理性
- 内存泄漏风险

### 3. 实际性能测试
- 基准测试建议
- 性能指标定义
- 测试数据准备

### 4. 优化建议
- 算法优化方案
- 数据结构改进
- 缓存策略建议
- 并发处理优化

请提供详细的性能分析报告和优化方案。
```

## 安全分析

```
请对以下代码进行安全分析：
代码：{代码片段}
类型：{Web应用/API/数据处理等}

安全检查项：

### 1. 输入验证
- 用户输入的验证和过滤
- SQL注入防护
- XSS攻击防护
- CSRF保护

### 2. 身份认证和授权
- 认证机制的安全性
- 权限控制的完整性
- 会话管理的安全性
- 密码存储和传输

### 3. 数据保护
- 敏感数据的加密
- 数据传输的安全性
- 日志中的敏感信息
- 数据备份和恢复

### 4. 系统安全
- 依赖库的安全漏洞
- 配置文件的安全性
- 错误信息的泄露
- 系统资源的保护

请提供详细的安全分析报告和修复建议。
```

## 使用指南

### 1. 选择合适的分析模板
- 项目初期使用"项目初始化分析"
- 功能开发使用"特定功能分析"
- 问题排查使用"问题诊断分析"
- 性能优化使用"性能分析"
- 安全审计使用"安全分析"

### 2. 自定义分析维度
- 根据项目特点调整分析重点
- 添加特定领域的分析要求
- 结合业务需求定制分析模板

### 3. 分析结果应用
- 基于分析结果制定改进计划
- 将分析发现转化为具体任务
- 建立持续改进的反馈机制
