# 代码编辑提示词模板

## 精确代码修改

```
请对以下代码进行修改：

### 修改需求
目标：{修改目标}
当前代码：
```{language}
{代码片段}
```

### 修改要求
1. **保持一致性**
   - 遵循现有的代码风格和命名规范
   - 保持缩进、空格、换行的一致性
   - 使用项目中已有的模式和约定

2. **影响分析**
   - 分析修改对其他模块的影响
   - 识别需要同步更新的相关代码
   - 考虑向后兼容性问题

3. **质量保证**
   - 确保修改不引入新的bug
   - 保持或提升代码的可读性
   - 添加必要的注释和文档

4. **输出格式**
   - 提供完整的修改后代码
   - 标注主要修改点
   - 解释修改的原因和好处
   - 给出测试建议

请按照以上要求进行修改，并提供详细的修改说明。
```

## 功能增强

```
请为以下代码添加新功能：

### 基础信息
现有代码：
```{language}
{代码片段}
```

新功能需求：{功能描述}

### 实现要求
1. **功能设计**
   - 分析新功能的输入输出
   - 设计合适的接口和参数
   - 考虑错误处理和边界条件

2. **集成方式**
   - 选择最小侵入的集成方式
   - 保持现有功能的稳定性
   - 确保新旧功能的协调性

3. **代码质量**
   - 遵循SOLID原则
   - 使用合适的设计模式
   - 保持代码的可测试性

4. **文档和测试**
   - 添加必要的注释和文档
   - 提供使用示例
   - 建议相应的测试用例

请提供完整的实现方案和代码。
```

## 代码重构

```
请对以下代码进行重构：

### 重构目标
代码：
```{language}
{代码片段}
```

重构目的：{重构原因}

### 重构原则
1. **保持功能不变**
   - 确保重构后功能完全一致
   - 保持对外接口的兼容性
   - 维持性能特征

2. **提升代码质量**
   - 消除代码异味
   - 提高可读性和可维护性
   - 减少重复代码

3. **改进设计**
   - 应用合适的设计模式
   - 优化类和方法的职责分配
   - 改善模块间的耦合关系

4. **渐进式重构**
   - 分步骤进行重构
   - 每步都保持代码可运行
   - 提供重构路径和验证方法

请提供详细的重构方案和实施步骤。
```

## 性能优化

```
请对以下代码进行性能优化：

### 优化目标
代码：
```{language}
{代码片段}
```

性能问题：{性能问题描述}
使用场景：{使用场景}

### 优化策略
1. **算法优化**
   - 分析当前算法的时间复杂度
   - 寻找更高效的算法实现
   - 优化数据结构的选择

2. **资源优化**
   - 减少内存分配和释放
   - 优化I/O操作
   - 合理使用缓存机制

3. **并发优化**
   - 识别可并行化的操作
   - 使用合适的并发模式
   - 避免锁竞争和死锁

4. **系统优化**
   - 优化数据库查询
   - 减少网络请求
   - 使用连接池等资源池

请提供优化后的代码和性能改进说明。
```

## 错误修复

```
请修复以下代码中的错误：

### 错误信息
代码：
```{language}
{代码片段}
```

错误描述：{错误描述}
错误日志：{错误日志}

### 修复要求
1. **问题定位**
   - 分析错误的根本原因
   - 识别错误的触发条件
   - 确定修复的范围

2. **解决方案**
   - 提供多种修复方案
   - 评估每种方案的优缺点
   - 选择最佳的修复策略

3. **预防措施**
   - 添加适当的错误处理
   - 增强输入验证
   - 改进异常处理机制

4. **验证方法**
   - 提供测试用例验证修复
   - 建议回归测试范围
   - 给出监控和告警建议

请提供修复后的代码和详细说明。
```

## 代码审查

```
请对以下代码进行审查：

### 审查代码
```{language}
{代码片段}
```

### 审查维度
1. **功能正确性**
   - 逻辑是否正确
   - 边界条件处理
   - 错误处理是否完善

2. **代码质量**
   - 可读性和可维护性
   - 命名规范和注释
   - 代码结构和组织

3. **性能考虑**
   - 算法效率
   - 资源使用
   - 潜在的性能瓶颈

4. **安全性**
   - 输入验证
   - 权限检查
   - 敏感信息处理

5. **最佳实践**
   - 设计模式应用
   - 编码规范遵循
   - 测试友好性

请提供详细的审查报告和改进建议。
```

## 使用指南

### 1. 选择合适的编辑模板
- **精确修改**: 小范围的代码调整
- **功能增强**: 添加新的功能特性
- **代码重构**: 改善代码结构和质量
- **性能优化**: 解决性能问题
- **错误修复**: 修复bug和异常
- **代码审查**: 质量检查和改进建议

### 2. 自定义编辑要求
- 根据项目特点调整编辑标准
- 添加特定的质量要求
- 结合团队规范定制模板

### 3. 编辑后验证
- 运行相关测试用例
- 检查代码风格一致性
- 验证功能完整性
- 评估性能影响
