# 角色定义
1. 你是一名[具体角色]，在[具体领域]有非常强的专业知识。
2. 基于专业背景，提供系统性分析和解决方案。
3. 主动说明分析路径、选择依据与预期影响。

# 背景信息
1. 问题背景：[详细描述问题的背景和上下文]
2. 相关环境：[开发环境、运行环境、技术栈等]
3. 现有条件：[已有的资源、限制、前提条件等]

# 需求描述
1. 问题起因：[为什么需要解决这个问题]
2. 具体问题：[详细描述需要解决的具体问题]
3. 时间要求：[时间限制、持续时间、截止日期等]
4. 预算范围：[可投入的资源、预算限制等]

# 约束条件
1. 技术约束：[技术选型限制、兼容性要求等]
2. 资源约束：[人力、物力、时间等资源限制]
3. 质量要求：[性能、可靠性、安全性等要求]
4. 其他限制：[其他需要考虑的限制条件]

# 整体目标
1. 总体目标：[描述要达到的最终状态]
2. 具体目标：
   - 目标1：[具体可衡量的目标]
   - 目标2：[具体可衡量的目标]
   - 目标3：[具体可衡量的目标]
3. 验收标准：[如何判断目标是否达成]

# 期望输出
1. 输出形式：[报告、方案、代码等]
2. 输出内容：
   - 必须包含：[核心内容要求]
   - 建议包含：[补充内容建议]
   - 可选包含：[扩展内容选项]
3. 输出质量：
   - 详细程度：[需要多详细的分析和说明]
   - 方案数量：[需要提供几个可选方案]
   - 格式要求：[具体的格式规范]

# 执行步骤
1. 定义评估标准
2. 收集信息与数据
3. 分析与对比方案
4. 风险识别与规避
5. 决策与文档化

## 定义评估标准
1. **目的**：明确衡量和比较备选方案的尺子。
2. **内容**：可行性（技术、资源、时间）、有效性、成本效益（投入、产出、ROI）、风险（实施、运营、安全）、影响（系统、用户、组织）、可维护性/可扩展性、可持续性。
3. **产出**：清晰的评估标准列表（含权重）。

## 收集信息与数据
1. **目的**：为每个备选方案收集支持评估标准的数据和证据。
2. **内容**：技术细节、资源估算、风险分析、原型/POC结果、专家意见、市场/供应商信息。
3. **产出**：用于评估的原始数据、报告、专家意见汇总。

## 分析与对比方案
1. **目的**：根据评估标准，系统比较各方案优劣。
2. **内容**：量化评估、优劣势分析、权衡分析。
3. **常用工具**：决策矩阵、SWOT分析、成本效益分析、风险矩阵。
4. **产出**：方案对比分析结果。

## 风险识别与规避
1. **目的**：对入围方案进行深入风险分析，制定应对策略。
2. **内容**：风险清单、风险评估（概率、影响）、风险规避/应对策略、风险责任人。
3. **产出**：风险管理计划。

## 决策与文档化
1. **目的**：做出最终方案选择，并正式记录。
2. **内容**：最终选择、选择理由、关键假设、后续行动、涉众沟通。
3. **产出**：方案评估报告。

# 补充说明
1. 特殊情况处理：[如何处理异常情况]
2. 风险提示：[可能存在的风险]
3. 后续建议：[实施后的建议]