# 角色定义
1. 你是一名[具体角色]，在[具体领域]有非常强的专业知识。
2. 基于专业背景，提供系统性分析和解决方案。
3. 主动说明分析路径、选择依据与预期影响。

# 背景信息
1. 问题背景：[详细描述问题的背景和上下文]
2. 相关环境：[开发环境、运行环境、技术栈等]
3. 现有条件：[已有的资源、限制、前提条件等]

# 需求描述
1. 问题起因：[为什么需要解决这个问题]
2. 具体问题：[详细描述需要解决的具体问题]
3. 时间要求：[时间限制、持续时间、截止日期等]
4. 预算范围：[可投入的资源、预算限制等]

# 约束条件
1. 技术约束：[技术选型限制、兼容性要求等]
2. 资源约束：[人力、物力、时间等资源限制]
3. 质量要求：[性能、可靠性、安全性等要求]
4. 其他限制：[其他需要考虑的限制条件]

# 整体目标
1. 总体目标：[描述要达到的最终状态]
2. 具体目标：
   - 目标1：[具体可衡量的目标]
   - 目标2：[具体可衡量的目标]
   - 目标3：[具体可衡量的目标]
3. 验收标准：[如何判断目标是否达成]

# 期望输出
1. 输出形式：[报告、方案、代码等]
2. 输出内容：
   - 必须包含：[核心内容要求]
   - 建议包含：[补充内容建议]
   - 可选包含：[扩展内容选项]
3. 输出质量：
   - 详细程度：[需要多详细的分析和说明]
   - 方案数量：[需要提供几个可选方案]
   - 格式要求：[具体的格式规范]

# 执行步骤
1. 明确问题
2. 收集数据
3. 分析数据与寻找根本原因
4. 提出解决方案/纠正措施

## 明确问题
1. **步骤**：对比实际与预期，清晰定义问题。
2. **内容**：问题描述、影响、范围、严重性。
3. **产出**：清晰、量化、可衡量的问题陈述。

## 收集数据
1. **步骤**：系统收集相关定性/定量数据。
2. **内容**：时间、地点、人员、方法、设备、环境、历史数据。
3. **产出**：原始数据、数据收集表格。

## 分析数据与寻找根本原因
1. **步骤**：运用分析工具，挖掘根本原因。
2. **内容**：识别相关性、排除无关因素、找出因果链条、根本原因。
3. **常用工具**：5Why、鱼骨图、帕累托图、散点图、直方图、控制图、FMEA。
4. **产出**：数据分析报告、根本原因列表、因果图。

## 提出解决方案/纠正措施
1. **步骤**：基于根本原因，提出针��性解决方案。
2. **内容**：解决方案清单、可行性评估、优先级排序。
3. **产出**：建议的解决方案列表。

# 补充说明
1. 特殊情况处理：[如何处理异常情况]
2. 风险提示：[可能存在的风险]
3. 后续建议：[实施后的建议]