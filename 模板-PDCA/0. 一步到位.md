# 角色定义
1. 你是一名[具体角色]，在[具体领域]有非常强的专业知识。
2. 基于专业背景，提供系统性分析和解决方案。
3. 主动说明分析路径、选择依据与预期影响。

# 背景信息
1. 问题背景：[详细描述问题的背景和上下文]
2. 相关环境：[开发环境、运行环境、技术栈等]
3. 现有条件：[已有的资源、限制、前提条件等]

# 需求描述
1. 问题起因：[为什么需要解决这个问题]
2. 具体问题：[详细描述需要解决的具体问题]
3. 时间要求：[时间限制、持续时间、截止日期等]
4. 预算范围：[可投入的资源、预算限制等]

# 约束条件
1. 技术约束：[技术选型限制、兼容性要求等]
2. 资源约束：[人力、物力、时间等资源限制]
3. 质量要求：[性能、可靠性、安全性等要求]
4. 其他限制：[其他需要考虑的限制条件]

# 整体目标
1. 总体目标：[描述要达到的最终状态]
2. 具体目标：
   - 目标1：[具体可衡量的目标]
   - 目标2：[具体可衡量的目标]
   - 目标3：[具体可衡量的目标]
3. 验收标准：[如何判断目标是否达成]

# 期望输出
1. 输出形式：[报告、方案、代码等]
2. 输出内容：
   - 必须包含：[核心内容要求]
   - 建议包含：[补充内容建议]
   - 可选包含：[扩展内容选项]
3. 输出质量：
   - 详细程度：[需要多详细的分析和说明]
   - 方案数量：[需要提供几个可选方案]
   - 格式要求：[具体的格式规范]

# 执行步骤
1. 问题分析
2. 方案设计
3. 方案评估

## 问题分析 (Plan)
1. **明确问题**：对比实际与预期，清晰定义问题（描述、影响、范围、严重性）。
2. **收集数据**：系统收集相关定性/定量数据（时间、地点、人员、方法、设备、环境、历史）。
3. **分析数据与寻找根本原因**：运用5Why、鱼骨图、帕累托图等工具，识别相关性，排除无关因素，找出因果链条及根本原因。
4. **提出解决方案/纠正措施**：基于根本原因，提出针对性解决方案，初步评估可行性、成本、风险、效果，并排序。

## 方案设计 (Do)
1. **方案构思与初步生成**：提出多个可行方案。
2. **方案细化与详细设计**：绘制流程图、架构图，定义模块、接口，考虑技术选型、数据结构、算法，预估性能与资源。
3. **方案初步分析与对比**：从经济、技术、风险等角度对比各方案优缺点。

## 方案评估 (Check)
1. **定义评估标准**：明确评估维度（可行性、有效性、成本效益、风险、影响、可维护性、可持续性），并设定权重。
2. **收集信息与数据**：为各方案收集技术细节、资源估算、风险分析、原型结果、专家意见等。
3. **分析与对比方案**：量化评估，进行优劣势、权衡分析，使用决策矩阵、SWOT、CBA等工具。
4. **风险识别与规避**：针对入围方案，深入分析风险，制定规避/应对策略，明确责任人。
5. **决策与文档化**：选择最终方案，阐述选择理由、关键假设，明确后续行动，并向涉众汇报。

# 评价标准
1. 完整性：方案是否覆盖所有需求点。
2. 技术可行性：方案是否在约束条件下可行。
3. 经济可行性：投入产出比是否合理。
4. 可维护性：方案是否易于实施和维护。

# 补充说明
1. 特殊情况处理：[如何处理异常情况]
2. 风险提示：[可能存在的风险]
3. 后续建议：[实施后的建议]