# 角色定义
1. 你是一名[具体角色]，在[具体领域]有非常强的专业知识。
2. 基于专业背景，提供系统性分析和解决方案。
3. 主动说明分析路径、选择依据与预期影响。

# 背景信息
1. 问题背景：[详细描述问题的背景和上下文]
2. 相关环境：[开发环境、运行环境、技术栈等]
3. 现有条件：[已有的资源、限制、前提条件等]

# 需求描述
1. 问题起因：[为什么需要解决这个问题]
2. 具体问题：[详细描述需要解决的具体问题]
3. 时间要求：[时间限制、持续时间、截止日期等]
4. 预算范围：[可投入的资源、预算限制等]

# 约束条件
1. 技术约束：[技术选型限制、兼容性要求等]
2. 资源约束：[人力、物力、时间等资源限制]
3. 质量要求：[性能、可靠性、安全性等要求]
4. 其他限制：[其他需要考虑的限制条件]

# 整体目标
1. 总体目标：[描述要达到的最终状态]
2. 具体目标：
   - 目标1：[具体可衡量的目标]
   - 目标2：[具体可衡量的目标]
   - 目标3：[具体可衡量的目标]
3. 验收标准：[如何判断目标是否达成]

# 期望输出
1. ���出形式：[报告、方案、代码等]
2. 输出内容：
   - 必须包含：[核心内容要求]
   - 建议包含：[补充内容建议]
   - 可选包含：[扩展内容选项]
3. 输出质量：
   - 详细程度：[需要多详细的分析和说明]
   - 方案数量：[需要提供几个可选方案]
   - 格式要求：[具体的格式规范]

# 执行步骤
1. 方案构思与初步生成
2. 方案细化与详细设计
3. 方案初步分析与对比

## 方案构思与初步生成
1. **步骤**：提出多个可行方案。
2. **产出**：初步方案列表。

## 方案细化与详细设计
1. **目的**：将有潜力方案转化为可操作、可执行的详细设计。
2. **内容**：绘制流程图、架构图，定义模块、接口，考虑技术选型、数据结构、算法，预估性能与资源。
3. **产出**：详细方案设计文档、初步资源需求清单和时间预估。

## 方案初步分析与对比
1. **目的**：初步分析各方案优缺点。
2. **内容**：从经济、技术、风险等角度进行对比。
3. **产出**：方案对比分析结果。

# 补充说明
1. 特殊情况处理：[如何处理异常情况]
2. 风险提示：[可能存在的风险]
3. 后续建议：[实施后的建议]